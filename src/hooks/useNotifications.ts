import { useState, useEffect, useCallback, useRef } from 'react';
import { Platform, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthContext } from '../contexts/AuthContext';
import { supabase } from '../utils/supabase';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

interface NotificationState {
  expoPushToken: string | null;
  notification: Notifications.Notification | null;
  permissionGranted: boolean;
  loading: boolean;
  error: string | null;
}

const NOTIFICATION_SETTINGS_KEY = 'notification_settings';

export const useNotifications = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<NotificationState>({
    expoPushToken: null,
    notification: null,
    permissionGranted: false,
    loading: true,
    error: null,
  });

  const notificationListener = useRef<Notifications.Subscription | null>(null);
  const responseListener = useRef<Notifications.Subscription | null>(null);

  // Register for push notifications
  const registerForPushNotifications = useCallback(async (): Promise<string | null> => {
    let token = null;

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        Alert.alert(
          'Notifications Disabled',
          'Enable notifications in settings to receive daily bonus reminders and special offers!'
        );
        return null;
      }

      try {
        const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
        if (!projectId) {
          throw new Error('Project ID not found');
        }

        token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
        console.log('Expo push token:', token);

        // Save token to Supabase for sending notifications
        if (user && token) {
          await supabase
            .from('users')
            .update({ push_token: token })
            .eq('id', user.id);
        }

      } catch (error) {
        console.error('Error getting push token:', error);
      }
    } else {
      Alert.alert('Must use physical device for Push Notifications');
    }

    return token;
  }, [user]);

  // Initialize notifications
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));

        const token = await registerForPushNotifications();
        const { status } = await Notifications.getPermissionsAsync();

        setState(prev => ({
          ...prev,
          expoPushToken: token,
          permissionGranted: status === 'granted',
          loading: false,
        }));

      } catch (error) {
        console.error('Error initializing notifications:', error);
        setState(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to initialize notifications',
        }));
      }
    };

    initializeNotifications();
  }, [registerForPushNotifications]);

  // Set up notification listeners
  useEffect(() => {
    // Listen for notifications received while app is foregrounded
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      setState(prev => ({ ...prev, notification }));
    });

    // Listen for user interactions with notifications
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      
      // Handle notification tap based on type
      if (data?.type === 'daily_bonus') {
        // Navigate to daily bonus screen
        console.log('Navigate to daily bonus');
      } else if (data?.type === 'quest_complete') {
        // Navigate to quests screen
        console.log('Navigate to quests');
      }
    });

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  // Schedule local notification
  const scheduleLocalNotification = useCallback(async (
    title: string,
    body: string,
    data: any = {},
    trigger: Notifications.NotificationTriggerInput
  ): Promise<string | null> => {
    try {
      if (!state.permissionGranted) {
        console.warn('Notifications not permitted');
        return null;
      }

      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger,
      });

      console.log('Scheduled notification:', id);
      return id;

    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  }, [state.permissionGranted]);

  // Schedule daily bonus reminder
  const scheduleDailyBonusReminder = useCallback(async (): Promise<string | null> => {
    try {
      // Cancel existing daily bonus notifications
      await Notifications.cancelScheduledNotificationAsync('daily_bonus_reminder');

      // Schedule for tomorrow at 9 AM
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(9, 0, 0, 0);

      return await scheduleLocalNotification(
        '🎁 Daily Bonus Available!',
        'Your daily bonus is ready to claim. Don\'t break your streak!',
        { type: 'daily_bonus' },
        {
          type: Notifications.SchedulableTriggerInputTypes.DATE,
          date: tomorrow,
          repeats: true,
        }
      );

    } catch (error) {
      console.error('Error scheduling daily bonus reminder:', error);
      return null;
    }
  }, [scheduleLocalNotification]);

  // Schedule quest completion notification
  const scheduleQuestReminder = useCallback(async (
    questTitle: string,
    hoursFromNow: number = 2
  ): Promise<string | null> => {
    const triggerTime = new Date();
    triggerTime.setHours(triggerTime.getHours() + hoursFromNow);

    return await scheduleLocalNotification(
      '🎯 Quest Progress',
      `Don't forget to complete "${questTitle}" before it expires!`,
      { type: 'quest_reminder' },
      {
        type: Notifications.SchedulableTriggerInputTypes.DATE,
        date: triggerTime
      }
    );
  }, [scheduleLocalNotification]);

  // Schedule low coins notification
  const scheduleLowCoinsNotification = useCallback(async (): Promise<string | null> => {
    const triggerTime = new Date();
    triggerTime.setMinutes(triggerTime.getMinutes() + 30); // 30 minutes from now

    return await scheduleLocalNotification(
      '🪙 Running Low on Coins?',
      'Visit the shop for great coin packages and special offers!',
      { type: 'low_coins' },
      {
        type: Notifications.SchedulableTriggerInputTypes.DATE,
        date: triggerTime
      }
    );
  }, [scheduleLocalNotification]);

  // Cancel all scheduled notifications
  const cancelAllNotifications = useCallback(async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Error cancelling notifications:', error);
    }
  }, []);

  // Get notification settings
  const getNotificationSettings = useCallback(async () => {
    try {
      const settings = await AsyncStorage.getItem(NOTIFICATION_SETTINGS_KEY);
      return settings ? JSON.parse(settings) : {
        dailyBonus: true,
        questReminders: true,
        specialOffers: true,
        lowCoins: true,
      };
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return {
        dailyBonus: true,
        questReminders: true,
        specialOffers: true,
        lowCoins: true,
      };
    }
  }, []);

  // Update notification settings
  const updateNotificationSettings = useCallback(async (settings: any) => {
    try {
      await AsyncStorage.setItem(NOTIFICATION_SETTINGS_KEY, JSON.stringify(settings));
      
      // Reschedule notifications based on new settings
      if (settings.dailyBonus) {
        await scheduleDailyBonusReminder();
      } else {
        await Notifications.cancelScheduledNotificationAsync('daily_bonus_reminder');
      }

    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  }, [scheduleDailyBonusReminder]);

  // Send immediate notification (for testing)
  const sendTestNotification = useCallback(async () => {
    return await scheduleLocalNotification(
      '🧪 Test Notification',
      'This is a test notification from Jackpot Party!',
      { type: 'test' },
      {
        type: Notifications.SchedulableTriggerInputTypes.TIME_INTERVAL,
        seconds: 1
      }
    );
  }, [scheduleLocalNotification]);

  return {
    expoPushToken: state.expoPushToken,
    notification: state.notification,
    permissionGranted: state.permissionGranted,
    loading: state.loading,
    error: state.error,
    scheduleLocalNotification,
    scheduleDailyBonusReminder,
    scheduleQuestReminder,
    scheduleLowCoinsNotification,
    cancelAllNotifications,
    getNotificationSettings,
    updateNotificationSettings,
    sendTestNotification,
  };
};
