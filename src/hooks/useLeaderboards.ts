import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

export interface LeaderboardEntry {
  id: string;
  user_id: string;
  username: string | null;
  avatar_url: string | null;
  leaderboard_type: 'weekly_coins' | 'monthly_coins' | 'all_time_coins' | 'level';
  score: number;
  rank: number;
  period_start: string;
  period_end: string;
  created_at: string;
  updated_at: string;
}

interface LeaderboardsState {
  weeklyCoins: LeaderboardEntry[];
  monthlyCoins: LeaderboardEntry[];
  allTimeCoins: LeaderboardEntry[];
  levels: LeaderboardEntry[];
  userRankings: {
    weekly_coins?: number;
    monthly_coins?: number;
    all_time_coins?: number;
    level?: number;
  };
  loading: boolean;
  error: string | null;
}

export const useLeaderboards = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<LeaderboardsState>({
    weeklyCoins: [],
    monthlyCoins: [],
    allTimeCoins: [],
    levels: [],
    userRankings: {},
    loading: true,
    error: null,
  });

  // Load leaderboards from Supabase
  const loadLeaderboards = useCallback(async () => {
    if (!isAuthenticated) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Get current periods
      const now = new Date();
      const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      // Load all leaderboard types
      const [weeklyResult, monthlyResult, allTimeResult, levelsResult] = await Promise.all([
        // Weekly coins leaderboard
        supabase
          .from('leaderboards')
          .select(`
            *,
            users!inner(username, avatar_url)
          `)
          .eq('leaderboard_type', 'weekly_coins')
          .gte('period_start', weekStart.toISOString())
          .order('rank', { ascending: true })
          .limit(100),

        // Monthly coins leaderboard
        supabase
          .from('leaderboards')
          .select(`
            *,
            users!inner(username, avatar_url)
          `)
          .eq('leaderboard_type', 'monthly_coins')
          .gte('period_start', monthStart.toISOString())
          .order('rank', { ascending: true })
          .limit(100),

        // All-time coins leaderboard
        supabase
          .from('leaderboards')
          .select(`
            *,
            users!inner(username, avatar_url)
          `)
          .eq('leaderboard_type', 'all_time_coins')
          .order('rank', { ascending: true })
          .limit(100),

        // Levels leaderboard
        supabase
          .from('leaderboards')
          .select(`
            *,
            users!inner(username, avatar_url)
          `)
          .eq('leaderboard_type', 'level')
          .order('rank', { ascending: true })
          .limit(100),
      ]);

      // Process results and add username/avatar
      const processLeaderboard = (result: any) => {
        if (result.error) throw result.error;
        return (result.data || []).map((entry: any) => ({
          ...entry,
          username: entry.users?.username,
          avatar_url: entry.users?.avatar_url,
        }));
      };

      const weeklyCoins = processLeaderboard(weeklyResult);
      const monthlyCoins = processLeaderboard(monthlyResult);
      const allTimeCoins = processLeaderboard(allTimeResult);
      const levels = processLeaderboard(levelsResult);

      // Get user rankings
      const userRankings: any = {};
      if (user) {
        [weeklyCoins, monthlyCoins, allTimeCoins, levels].forEach((leaderboard, index) => {
          const types = ['weekly_coins', 'monthly_coins', 'all_time_coins', 'level'];
          const userEntry = leaderboard.find((entry: LeaderboardEntry) => entry.user_id === user.id);
          if (userEntry) {
            userRankings[types[index]] = userEntry.rank;
          }
        });
      }

      setState(prev => ({
        ...prev,
        weeklyCoins,
        monthlyCoins,
        allTimeCoins,
        levels,
        userRankings,
        loading: false,
      }));

    } catch (error) {
      console.error('Error loading leaderboards:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load leaderboards',
      }));
    }
  }, [isAuthenticated, user]);

  // Update user score for a specific leaderboard
  const updateUserScore = useCallback(async (
    leaderboardType: LeaderboardEntry['leaderboard_type'],
    score: number
  ): Promise<boolean> => {
    if (!isAuthenticated || !user) return false;

    try {
      // Get current period dates
      const now = new Date();
      let periodStart: Date;
      let periodEnd: Date;

      switch (leaderboardType) {
        case 'weekly_coins':
          periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
          periodEnd = new Date(periodStart.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        case 'monthly_coins':
          periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
          periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);
          break;
        case 'all_time_coins':
        case 'level':
          periodStart = new Date(2024, 0, 1); // App launch date
          periodEnd = new Date(2030, 0, 1); // Far future
          break;
      }

      // Upsert leaderboard entry
      const { error } = await supabase
        .from('leaderboards')
        .upsert({
          user_id: user.id,
          leaderboard_type: leaderboardType,
          score,
          period_start: periodStart.toISOString(),
          period_end: periodEnd.toISOString(),
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id,leaderboard_type,period_start',
        });

      if (error) {
        throw error;
      }

      // Trigger rank recalculation (this would typically be done by a database trigger or cron job)
      await recalculateRanks(leaderboardType, periodStart.toISOString());

      return true;

    } catch (error) {
      console.error('Error updating user score:', error);
      return false;
    }
  }, [isAuthenticated, user]);

  // Recalculate ranks for a leaderboard (simplified version)
  const recalculateRanks = useCallback(async (
    leaderboardType: LeaderboardEntry['leaderboard_type'],
    periodStart: string
  ) => {
    try {
      // This is a simplified version - in production, this should be done server-side
      const { data, error } = await supabase
        .from('leaderboards')
        .select('id, score')
        .eq('leaderboard_type', leaderboardType)
        .gte('period_start', periodStart)
        .order('score', { ascending: false });

      if (error) throw error;

      // Update ranks
      const updates = (data || []).map((entry, index) => ({
        id: entry.id,
        rank: index + 1,
      }));

      for (const update of updates) {
        await supabase
          .from('leaderboards')
          .update({ rank: update.rank })
          .eq('id', update.id);
      }

    } catch (error) {
      console.error('Error recalculating ranks:', error);
    }
  }, []);

  // Get user's position in a specific leaderboard
  const getUserRank = useCallback((
    leaderboardType: LeaderboardEntry['leaderboard_type']
  ): number | null => {
    return state.userRankings[leaderboardType] || null;
  }, [state.userRankings]);

  // Get top N entries from a leaderboard
  const getTopEntries = useCallback((
    leaderboardType: LeaderboardEntry['leaderboard_type'],
    limit: number = 10
  ): LeaderboardEntry[] => {
    const leaderboard = state[leaderboardType === 'weekly_coins' ? 'weeklyCoins' :
                              leaderboardType === 'monthly_coins' ? 'monthlyCoins' :
                              leaderboardType === 'all_time_coins' ? 'allTimeCoins' :
                              'levels'];
    return leaderboard.slice(0, limit);
  }, [state]);

  // Load leaderboards when component mounts or user changes
  useEffect(() => {
    loadLeaderboards();
  }, [loadLeaderboards]);

  // Subscribe to real-time leaderboard updates
  useEffect(() => {
    if (!isAuthenticated) return;

    const subscription = supabase
      .channel('leaderboards')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'leaderboards',
        },
        (payload) => {
          console.log('Leaderboard update received:', payload);
          
          // Reload leaderboards when changes occur
          // In a production app, you might want to be more selective about when to reload
          loadLeaderboards();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [isAuthenticated, loadLeaderboards]);

  return {
    weeklyCoins: state.weeklyCoins,
    monthlyCoins: state.monthlyCoins,
    allTimeCoins: state.allTimeCoins,
    levels: state.levels,
    userRankings: state.userRankings,
    loading: state.loading,
    error: state.error,
    updateUserScore,
    getUserRank,
    getTopEntries,
    refresh: loadLeaderboards,
  };
};
