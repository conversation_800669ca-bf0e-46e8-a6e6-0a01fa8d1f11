import { useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

export interface RealTimeNotification {
  id: string;
  type: 'quest_completed' | 'level_up' | 'achievement' | 'bonus_available' | 'friend_activity';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
}

interface UseRealTimeUpdatesProps {
  onQuestCompleted?: (questId: string) => void;
  onLevelUp?: (newLevel: number) => void;
  onAchievementUnlocked?: (achievementId: string) => void;
  onBonusAvailable?: () => void;
  onNotification?: (notification: RealTimeNotification) => void;
}

export const useRealTimeUpdates = ({
  onQuestCompleted,
  onLevelUp,
  onAchievementUnlocked,
  onBonusAvailable,
  onNotification,
}: UseRealTimeUpdatesProps = {}) => {
  const { user, isAuthenticated } = useAuthContext();

  // Handle quest completion notifications
  const handleQuestUpdate = useCallback((payload: any) => {
    if (payload.eventType === 'UPDATE' && payload.new.completed && !payload.old.completed) {
      const questTitle = payload.new.title;
      const rewardCoins = payload.new.reward_coins;
      
      const notification: RealTimeNotification = {
        id: `quest_${payload.new.id}`,
        type: 'quest_completed',
        title: '🎯 Quest Completed!',
        message: `"${questTitle}" - Earned ${rewardCoins} coins!`,
        data: { questId: payload.new.id },
        timestamp: new Date().toISOString(),
      };

      onNotification?.(notification);
      onQuestCompleted?.(payload.new.id);

      // Show in-app notification
      Alert.alert(
        notification.title,
        notification.message,
        [{ text: 'Awesome!', style: 'default' }]
      );
    }
  }, [onQuestCompleted, onNotification]);

  // Handle level up notifications
  const handleUserUpdate = useCallback((payload: any) => {
    if (payload.eventType === 'UPDATE') {
      const oldLevel = payload.old.level;
      const newLevel = payload.new.level;
      
      if (newLevel > oldLevel) {
        const notification: RealTimeNotification = {
          id: `level_${newLevel}`,
          type: 'level_up',
          title: '🎉 Level Up!',
          message: `Congratulations! You reached level ${newLevel}!`,
          data: { oldLevel, newLevel },
          timestamp: new Date().toISOString(),
        };

        onNotification?.(notification);
        onLevelUp?.(newLevel);

        // Show celebration alert
        Alert.alert(
          notification.title,
          notification.message,
          [{ text: 'Amazing!', style: 'default' }]
        );
      }
    }
  }, [onLevelUp, onNotification]);

  // Handle daily bonus availability
  const handleDailyBonusCheck = useCallback(() => {
    if (!user) return;

    // Check if daily bonus is available (simplified check)
    const lastBonus = localStorage.getItem(`last_daily_bonus_${user.id}`);
    const today = new Date().toDateString();
    
    if (!lastBonus || lastBonus !== today) {
      const notification: RealTimeNotification = {
        id: 'daily_bonus',
        type: 'bonus_available',
        title: '🎁 Daily Bonus Available!',
        message: 'Your daily bonus is ready to claim!',
        data: {},
        timestamp: new Date().toISOString(),
      };

      onNotification?.(notification);
      onBonusAvailable?.();
    }
  }, [user, onBonusAvailable, onNotification]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    console.log('Setting up real-time subscriptions for user:', user.id);

    // Subscribe to quest updates
    const questsSubscription = supabase
      .channel(`quests_realtime_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'quests',
          filter: `user_id=eq.${user.id}`,
        },
        handleQuestUpdate
      )
      .subscribe();

    // Subscribe to user profile updates (for level changes)
    const userSubscription = supabase
      .channel(`user_realtime_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.id}`,
        },
        handleUserUpdate
      )
      .subscribe();

    // Subscribe to leaderboard updates (for competitive notifications)
    const leaderboardSubscription = supabase
      .channel('leaderboards_realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'leaderboards',
        },
        (payload) => {
          // Handle leaderboard position changes
          const newData = payload.new as any;
          if (newData?.user_id === user.id) {
            const notification: RealTimeNotification = {
              id: `leaderboard_${newData.id}`,
              type: 'achievement',
              title: '🏆 Leaderboard Update!',
              message: `You're now rank #${newData.rank} in ${newData.leaderboard_type}!`,
              data: { rank: newData.rank, type: newData.leaderboard_type },
              timestamp: new Date().toISOString(),
            };

            onNotification?.(notification);
          }
        }
      )
      .subscribe();

    // Check for daily bonus availability periodically
    const bonusCheckInterval = setInterval(handleDailyBonusCheck, 60000); // Check every minute

    // Cleanup subscriptions
    return () => {
      questsSubscription.unsubscribe();
      userSubscription.unsubscribe();
      leaderboardSubscription.unsubscribe();
      clearInterval(bonusCheckInterval);
    };
  }, [isAuthenticated, user, handleQuestUpdate, handleUserUpdate, handleDailyBonusCheck]);

  // Manual trigger for testing notifications
  const triggerTestNotification = useCallback((type: RealTimeNotification['type']) => {
    const testNotifications: Record<RealTimeNotification['type'], RealTimeNotification> = {
      quest_completed: {
        id: 'test_quest',
        type: 'quest_completed',
        title: '🎯 Test Quest Completed!',
        message: 'This is a test quest completion notification',
        timestamp: new Date().toISOString(),
      },
      level_up: {
        id: 'test_level',
        type: 'level_up',
        title: '🎉 Test Level Up!',
        message: 'This is a test level up notification',
        timestamp: new Date().toISOString(),
      },
      achievement: {
        id: 'test_achievement',
        type: 'achievement',
        title: '🏆 Test Achievement!',
        message: 'This is a test achievement notification',
        timestamp: new Date().toISOString(),
      },
      bonus_available: {
        id: 'test_bonus',
        type: 'bonus_available',
        title: '🎁 Test Bonus Available!',
        message: 'This is a test bonus notification',
        timestamp: new Date().toISOString(),
      },
      friend_activity: {
        id: 'test_friend',
        type: 'friend_activity',
        title: '👥 Test Friend Activity!',
        message: 'This is a test friend activity notification',
        timestamp: new Date().toISOString(),
      },
    };

    const notification = testNotifications[type];
    onNotification?.(notification);
    
    Alert.alert(notification.title, notification.message);
  }, [onNotification]);

  return {
    triggerTestNotification,
  };
};
