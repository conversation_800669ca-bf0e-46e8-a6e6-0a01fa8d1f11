import React, { useState } from 'react';
import { View, Text, Button, StyleSheet } from 'react-native';
import { AnimatedReel } from './AnimatedReel';
import { BetControl } from './BetControl';
import { SpinButton } from './SpinButton';
import { useAuth } from '../../hooks/useAuth';
import { supabase } from '../../utils/supabase';

// Example slot config (can be extended)
const slotConfig = {
  reels: 3,
  symbols: ['🍒', '🍋', '🔔', '7️⃣', '⭐'],
  minBet: 1,
  maxBet: 100,
};

export const SlotGameScreen = ({ gameId }: { gameId: string }) => {
  const { user } = useAuth();
  const [bet, setBet] = useState(slotConfig.minBet);
  const [reels, setReels] = useState<string[][]>([
    Array(slotConfig.reels).fill(''),
    Array(slotConfig.reels).fill(''),
    Array(slotConfig.reels).fill(''),
  ]);
  const [spinning, setSpinning] = useState(false);
  const [win, setWin] = useState<number | null>(null);
  // Add winningLines state for demo (all empty)
  const [winningLines, setWinningLines] = useState<number[]>([]);

  const handleSpin = async () => {
    if (!user) return;
    setSpinning(true);
    setWin(null);
    // Call Supabase Edge Function
    const { data, error } = await supabase.functions.invoke('spin_game', {
      body: {
        game_id: gameId,
        user_id: user.id,
        bet_amount: bet,
      },
    });
    setSpinning(false);
    if (error) {
      alert('Spin failed!');
      return;
    }
    setReels(data.reels);
    setWin(data.win_amount);
    // Handle bonuses if needed
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Slot Machine</Text>
      <View style={styles.reels}>
        {reels.map((reel, i) => (
          <AnimatedReel
            key={i}
            symbols={reel}
            spinning={spinning}
            winningLines={winningLines}
            reelIndex={i}
          />
        ))}
      </View>
      <BetControl
        betAmount={bet}
        onBetChange={setBet}
        maxBet={slotConfig.maxBet}
        disabled={spinning}
      />
      <SpinButton
        onPress={handleSpin}
        spinning={spinning}
        disabled={spinning}
        betAmount={bet}
      />
      {win !== null && <Text style={styles.win}>Win: {win}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, alignItems: 'center', justifyContent: 'center' },
  title: { fontSize: 32, fontWeight: 'bold', marginBottom: 20 },
  reels: { flexDirection: 'row', marginBottom: 20 },
  win: { fontSize: 24, color: 'green', marginTop: 20 },
});

export default SlotGameScreen;
