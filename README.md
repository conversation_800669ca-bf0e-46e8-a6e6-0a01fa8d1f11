# 🎰 Jackpot Party Casino

A complete React Native casino app built with Expo SDK 53 and Supabase backend. Features slot machines, daily bonuses, quests, leaderboards, and in-app purchases.

![React Native](https://img.shields.io/badge/React%20Native-0.73-blue.svg)
![Expo SDK](https://img.shields.io/badge/Expo%20SDK-53-black.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)
![Supabase](https://img.shields.io/badge/Supabase-2.0-green.svg)

## ✨ Features

### 🎮 Core Gameplay
- **5×3 Slot Machine** with 9 paylines and 95% RTP
- **Animated Reels** with react-native-reanimated v3
- **Auto-Spin** functionality with customizable counts
- **Progressive Betting** with preset amounts and max bet
- **Jackpot System** with 5x multiplier celebrations

### 💰 Economy & Progression
- **Real-time Coin Management** with Supabase sync
- **XP and Level System** with automatic progression
- **Daily Bonus Wheel** with streak multipliers
- **Quest System** with daily challenges
- **Leaderboards** with competitive rankings

### 🛒 Monetization
- **In-App Purchases** with RevenueCat integration
- **Coin Packages** with different value tiers
- **Special Offers** and limited-time deals
- **Purchase Restoration** and validation

### 🔔 Engagement
- **Push Notifications** for daily bonuses
- **Real-time Updates** for achievements
- **Tutorial System** for new users
- **Analytics Tracking** with Supabase

### 🔒 Privacy & Security
- **GDPR Compliance** with privacy consent
- **App Tracking Transparency** (iOS 14.5+)
- **Row Level Security** for data protection
- **Secure Authentication** with Supabase Auth

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Expo CLI: `npm install -g @expo/cli`
- iOS Simulator or Android Emulator
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/jackpot-party-casino.git
   cd jackpot-party-casino
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

4. **Set up Supabase**
   - Create a new project at [supabase.com](https://supabase.com)
   - Run the SQL schema from `supabase-schema.sql`
   - Get your project URL and anon key

5. **Start the development server**
   ```bash
   npm start
   ```

6. **Clear cache and restart (if needed)**
   ```bash
   # Clear Metro bundler cache
   npx expo start --clear

   # Full reset (if having persistent issues)
   rm -rf node_modules package-lock.json
   npm install
   npx expo start --clear
   ```

7. **Run on device**
   - Scan QR code with Expo Go app
   - Or press `i` for iOS simulator, `a` for Android emulator

## 📱 Screenshots

| Lobby Screen | Slot Machine | Daily Bonus | Shop |
|--------------|--------------|-------------|------|
| ![Lobby](docs/screenshots/lobby.png) | ![Slots](docs/screenshots/slots.png) | ![Bonus](docs/screenshots/bonus.png) | ![Shop](docs/screenshots/shop.png) |

## 🏗️ Architecture

### Frontend Stack
- **React Native + Expo SDK 53** - Cross-platform development
- **TypeScript** - Type safety and better DX
- **Expo Router** - File-based navigation
- **React Native Reanimated v3** - Smooth animations
- **React Native SVG** - Custom graphics
- **AsyncStorage** - Local persistence

### Backend Stack
- **Supabase** - PostgreSQL with real-time features
- **Row Level Security** - Data protection
- **Database Functions** - Server-side logic
- **Real-time Subscriptions** - Live updates

### Key Hooks
- `useCoins` - Coin management with real-time sync
- `useLevel` - XP and level progression
- `useSlotEngine` - Slot machine logic with 95% RTP
- `usePurchases` - In-app purchase handling
- `useQuests` - Daily challenge system
- `useAnalytics` - Event tracking
- `useNotifications` - Push notification management

## 🗄️ Database Schema

```sql
-- Core tables
users              -- User profiles, coins, XP, levels
game_sessions       -- Slot machine spins and results  
quests             -- Daily challenges and progress
purchases          -- In-app purchase history
leaderboards       -- Competitive rankings
analytics_events   -- Custom event tracking
```

## 🧪 Testing

```bash
# Run unit tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage

# Type checking
npm run type-check

# Linting
npm run lint
```

## � Troubleshooting

### Common Issues

**Metro bundler errors:**
```bash
# Clear Metro cache
npx expo start --clear

# Full reset
rm -rf node_modules package-lock.json .expo
npm install
npx expo start --clear
```

**TypeScript errors:**
```bash
# Check TypeScript
npm run type-check

# Restart TypeScript in VS Code
# Cmd+Shift+P -> "TypeScript: Restart TS Server"
```

**iOS/Android build issues:**
```bash
# iOS Simulator reset
xcrun simctl erase all

# Android clean
cd android && ./gradlew clean && cd ..
```

For more detailed troubleshooting, see [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md).

## �🚀 Deployment

### Development Build
```bash
eas build --profile development --platform all
```

### Production Build
```bash
eas build --profile production --platform all
```

### App Store Submission
```bash
# iOS
eas submit --platform ios --profile production

# Android
eas submit --platform android --profile production
```

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## 📊 Analytics

The app includes comprehensive analytics tracking:

- **User Events**: App opens, sessions, sign-ups
- **Gameplay**: Spins, wins, bet amounts, game types
- **Economy**: Purchases, coin usage, level progression
- **Engagement**: Quest completions, daily bonus claims

All analytics are stored in Supabase for easy querying and dashboard creation.

## 🔧 Configuration

### Environment Variables
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### App Configuration
Key settings in `app.config.js`:
- App name and bundle identifier
- Permissions and capabilities
- Splash screen and icon
- Build configurations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs](docs/) folder
- **Issues**: Open an issue on GitHub
- **Discussions**: Use GitHub Discussions for questions

## 🙏 Acknowledgments

- [Expo](https://expo.dev) - Amazing React Native toolchain
- [Supabase](https://supabase.com) - Backend as a Service
- [React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/) - Smooth animations
- [RevenueCat](https://revenuecat.com) - In-app purchase management

---

**Built with ❤️ using React Native + Expo + Supabase**

Ready to make your fortune? 🎰💰
