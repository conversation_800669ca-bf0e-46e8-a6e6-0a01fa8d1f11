// Supabase Edge Function: spin_game
// Place this in your Supabase Edge Functions directory (e.g., functions/spin_game/index.ts)

import { serve } from 'std/server';

serve(async (req) => {
  const { game_id, user_id, bet_amount } = await req.json();

  // TODO: Import and call your slot machine game engine here
  // Example:
  // const result = await gameEngine.spin({ game_id, user_id, bet_amount });

  // For demonstration, return a mock result
  const result = {
    reels: [
      ['🍒', '🍋', '7️⃣'],
      ['🍒', '🍋', '7️⃣'],
      ['🍒', '🍋', '7️⃣'],
    ],
    win_amount: Math.random() > 0.5 ? bet_amount * 2 : 0,
    bonuses: [],
  };

  return new Response(JSON.stringify(result), {
    headers: { 'Content-Type': 'application/json' },
  });
});
