{"name": "frontend", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "build:preview": "eas build --platform all --profile preview", "build:production": "eas build --platform all --profile production", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.1", "@reduxjs/toolkit": "^2.2.3", "@supabase/supabase-js": "^2.50.0", "axios": "^1.6.8", "dotenv": "^16.5.0", "expo": "~53.0.11", "expo-ads-admob": "^8.4.0", "expo-av": "^15.1.6", "expo-background-fetch": "^13.1.5", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-font": "^13.3.1", "expo-linking": "^7.1.5", "expo-notifications": "^0.31.3", "expo-router": "^5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "expo-tracking-transparency": "^5.2.4", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.11.0", "react-native-purchases": "^8.11.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-redux": "^9.1.2", "source-map": "^0.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.0", "jest": "^29.7.0", "typescript": "~5.8.3"}, "private": true}